[{"id": "1", "title": "Chatbot Development", "content": "Smart chatbots for WhatsApp, Facebook, and Websites.", "description": "We build customized chatbots for WhatsApp, Facebook, Instagram, and websites to automate customer support, lead generation, and sales processes. Quick delivery and affordable one-time setup.", "imageSrc": "/images/core-features/4.png", "date": "01", "month": "Aug"}, {"id": "2", "title": "AI Calling Agent", "content": "24/7 automated voice calling solutions.", "description": "Engage leads and customers through human-like calling agents that can answer queries, follow up, and book appointments automatically without human intervention.", "imageSrc": "/images/core-features/3.png", "date": "05", "month": "Aug"}, {"id": "3", "title": "Business Automation", "content": "End-to-end automation for business operations.", "description": "We automate repetitive tasks like lead nurturing, customer follow-ups, report generation, and notifications using advanced automation flows tailored to your business.", "imageSrc": "/images/core-features/1.png", "date": "12", "month": "Aug"}, {"id": "4", "title": "Power BI Dashboards", "content": "Interactive dashboards to track key metrics.", "description": "Our Power BI services include complete dashboard setup, connecting your sales, marketing, and operational data into a visualized, interactive reporting system accessible anytime.", "imageSrc": "/images/core-features/1.png", "date": "18", "month": "Aug"}, {"id": "5", "title": "Website Creation", "content": "Responsive and SEO-friendly websites.", "description": "We create business websites, landing pages, and e-commerce stores that are fast, mobile-responsive, SEO-optimized, and ready to generate leads for your business.", "imageSrc": "/images/core-features/2.png", "date": "25", "month": "Aug"}, {"id": "6", "title": "UI/UX & App Development", "content": "Modern apps and design systems for business.", "description": "We offer professional UI/UX design services and full-stack mobile app development to bring your ideas to life with a clean, intuitive, and functional user experience.", "imageSrc": "/images/core-features/5.png", "date": "30", "month": "Aug"}]