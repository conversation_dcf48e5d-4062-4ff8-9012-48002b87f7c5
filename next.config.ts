import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "ik.imagekit.io",

      },
      {
        protocol: "https",
        hostname: "html.tailus.io",

      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      }
    ]
  },
  eslint: {
    ignoreDuringBuilds: true,
  }
};

export default nextConfig;
